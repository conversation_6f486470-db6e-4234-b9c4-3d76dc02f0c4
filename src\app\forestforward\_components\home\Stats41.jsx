"use client";

import { Button1 } from "@/components/Button1";
import { Button } from "@relume_io/relume-ui";
import { motion, useInView, useMotionValue, useTransform, animate } from "framer-motion";
import React, { Fragment, useRef, useEffect } from "react";

// Counter component that animates from 0 to target value
function AnimatedCounter({ target, suffix = "", className = "" }) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });
  const count = useMotionValue(0);
  const rounded = useTransform(count, (latest) => Math.round(latest));

  useEffect(() => {
    if (isInView) {
      const controls = animate(count, target, {
        duration: 2,
        ease: "easeOut",
      });
      return controls.stop;
    }
  }, [isInView, count, target]);

  return (
    <motion.p ref={ref} className={className}>
      <motion.span>{rounded}</motion.span>
      {suffix}
    </motion.p>
  );
}

export function Stats41() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28 bg-white">
      <div className="container">
        <div className="mb-12 grid grid-cols-1 gap-y-5 md:mb-18 md:grid-cols-2 md:gap-x-12 lg:mb-20 lg:gap-x-20 border border-border rounded-lg p-20 bg-link text-white">
          <div>
            <h2 className="text-5xl font-semibold md:text-7xl lg:text-8xl">
              Visie
            </h2>
          </div>
          <div >
            <p className="md:text-md">
              Wij geloven in lokaal verduurzamen en vergroenen met een meetbaar
              verschil en langdurige betrokkenheid. Daarom combineren we
              natuurcreatie met beleving, educatie en wetenschappelijke
              opvolging. We hebben daarvoor het nodige netwerk en de juiste
              expertise in huis. Dus, jij bepaalt welke tastbare impact jullie
              willen wil maken, wij regelen de rest.
            </p>
            <div className="mt-6 flex flex-wrap items-center gap-4 md:mt-8">
              <Button1 title="Ontdek onze visie en aanpak" href={"/forestforward/visie"} variant="filled" className="rounded-lg">
                Ontdek onze visie en aanpak
              </Button1>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none border border-border rounded-lg">
              <h3 className="mb-8 text-md leading-[1.4] font-bold md:mb-10 md:text-xl lg:mb-12">
                Bedrijfsbossen
              </h3>
              <AnimatedCounter
                target={150}
                suffix="ha"
                className="text-right text-10xl leading-[1.3] font-bold md:text-[4rem] lg:text-[5rem] mt-auto"
              />
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">Bedrijfsbossen geplant</p>
            </div>
          </Fragment>
          <Fragment>
            <div>
              <img
                className="aspect-[3/2] size-full object-cover rounded-lg border border-border"
                src="/images/forestforward/homepage/11.png"
                alt="Relume placeholder image"
              />
            </div>
          </Fragment>
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none border border-border rounded-lg">
              <h3 className="mb-8 text-md leading-[1.4] font-bold md:mb-10 md:text-xl lg:mb-12">
                Bomen
              </h3>
              <AnimatedCounter
                target={350000}
                className="text-right text-10xl leading-[1.3] font-bold md:text-[4rem] lg:text-[5rem]"
              />
              
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">Bomen geplant</p>
            </div>
          </Fragment>
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none border border-border rounded-lg">
              <h3 className="mb-8 text-md leading-[1.4] font-bold md:mb-10 md:text-xl lg:mb-12">
                Schoolbossen
              </h3>
              <AnimatedCounter
                target={20}
                className="text-right text-10xl leading-[1.3] font-bold md:text-[4rem] lg:text-[5rem]"
              />
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">Schoolbossen geplant per jaar</p>
            </div>
          </Fragment>
          <Fragment>
            <div>
              <img
                className="aspect-[3/2] size-full rounded-lg object-cover border border-border"
                src="/images/forestforward/schoolbos/2.png"
                alt="Schoolbos foto"
              />
            </div>
          </Fragment>
        </div>
      </div>
    </section>
  );
}
