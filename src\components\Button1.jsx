"use client";

import React from "react";
import Link from "next/link";

export function Button1({
  children,
  variant = "filled",
  size = "md",
  className = "",
  title,
  onClick,
  href,
  target,
  rel,
  iconRight,
  ...props
}) {
  const sizeClasses = {
    sm: "px-6 py-2.5 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg",
    link: "px-0 py-1 text-sm"
  };

  const baseClasses = `group relative ${size === "link" ? "" : "overflow-hidden rounded-lg"} font-medium transition-all duration-300 ease-in-out active:scale-95 focus:outline-none ${sizeClasses[size]}`;
  
  const variantClasses = {
    filled: "border-none bg-background text-text-primary hover:bg-background-secondary hover:shadow-lg active:bg-background-tertiary focus:ring-1 focus:ring-background-secondary focus:ring-opacity-50",
    transparent: "border border-border-50 bg-transparent text-text-alternative hover:border-text-alternative hover:bg-text-alternative hover:text-text-primary active:bg-background-secondary active:text-text-primary focus:ring-1 focus:ring-text-alternative focus:ring-opacity-50",
    "transparent-light": "border border-border-50 bg-transparent text-text-primary hover:border-text-primary hover:bg-text-primary hover:text-text-alternative active:bg-background-secondary active:text-text-primary focus:ring-1 focus:ring-text-primary focus:ring-opacity-50",
    link: size === "link"
      ? "border-none bg-transparent text-link hover:text-link-primary focus:ring-1 focus:ring-link focus:ring-opacity-50"
      : "border-none bg-link text-text-alternative hover:bg-link-primary hover:shadow-lg active:bg-link-secondary focus:ring-1 focus:ring-link focus:ring-opacity-50",
  };

  const animationClasses = {
    filled: "absolute inset-0 bg-gradient-to-r from-background-primary to-background-primary transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100",
    transparent: "absolute inset-0 bg-text-alternative transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100",
    "transparent-light": "absolute inset-0 bg-text-primary transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100",
    link: size === "link"
      ? "" // No background animation for text-style links
      : "absolute inset-0 bg-link-primary transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100",
  };

  // Helper function to render button content with icon
  const renderContent = () => (
    <>
      <span className="relative z-10 transition-colors duration-300 flex items-center gap-x-2">
        {children}
        {iconRight && (
          <span className="transition-transform duration-300 ease-out group-hover:translate-x-1">
            {iconRight}
          </span>
        )}
      </span>
      {animationClasses[variant] && <div className={animationClasses[variant]}></div>}
    </>
  );

  // If href is provided, render as a link
  if (href) {
    // Check if it's an external link
    const isExternal = href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:');

    if (isExternal) {
      return (
        <a
          href={href}
          target={target}
          rel={rel}
          title={title}
          className={`${baseClasses} ${variantClasses[variant]} ${className} inline-block text-center`}
          {...props}
        >
          {renderContent()}
        </a>
      );
    } else {
      // Internal link - use Next.js Link
      return (
        <Link
          href={href}
          title={title}
          className={`${baseClasses} ${variantClasses[variant]} ${className} inline-block text-center`}
          {...props}
        >
          {renderContent()}
        </Link>
      );
    }
  }

  // Otherwise, render as a button
  return (
    <button
      title={title}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      onClick={onClick}
      {...props}
    >
      {renderContent()}
    </button>
  );
}
